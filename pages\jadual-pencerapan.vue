<template>
    <!-- Loading State -->
    <div v-if="loading" class="space-y-8">
        <SkeletonPageHeader :title-width="'18rem'" :subtitle-width="'50rem'" :action-count="1" />

        <!-- View Toggle Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-center sm:justify-end">
                <div class="w-full sm:w-auto">
                    <SkeletonBox height="2.5rem" width="12rem" class="rounded-md" />
                </div>
            </div>
        </div>

        <!-- Card View -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="card in 6" :key="`card-${card}`"
                class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <!-- Card Header -->
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 min-w-0 flex-1">
                            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
                            <SkeletonBox height="1.125rem" width="12rem" />
                        </div>
                    </div>
                </div>

                <!-- Card Content -->
                <div class="p-6 space-y-3">
                    <div v-for="field in 4" :key="`field-${field}`" class="flex items-center space-x-2">
                        <SkeletonBox height="1rem" width="1rem" class="rounded" />
                        <SkeletonBox height="0.875rem" width="4rem" variant="light" />
                        <SkeletonBox height="0.875rem" width="8rem" />
                    </div>

                    <!-- Notes field -->
                    <div class="flex items-start space-x-2">
                        <SkeletonBox height="1rem" width="1rem" class="rounded mt-0.5" />
                        <div class="min-w-0 flex-1 space-y-1">
                            <SkeletonBox height="0.875rem" width="4rem" variant="light" />
                            <SkeletonBox height="0.875rem" width="100%" variant="light" />
                            <SkeletonBox height="0.875rem" width="80%" variant="light" />
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <SkeletonBox height="1rem" width="1rem" class="rounded" />
                            <SkeletonBox height="0.875rem" width="4rem" variant="light" />
                        </div>
                        <SkeletonBox height="1.5rem" width="5rem" class="rounded-full" />
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-end space-x-2">
                        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div v-if="!loading" class="space-y-8">
        <!-- Page Header -->
        <UiCompositePageHeader title="Jadual Pencerapan PDPC Guru"
            subtitle="Urus jadual pencerapan pengajaran dan pembelajaran untuk pemantauan kualiti"
            icon="heroicons:eye-solid">
            <template #actions>
                <UiBaseButton variant="primary" size="sm" sm:size="md" prepend-icon="heroicons:plus-solid"
                    @click="showAddForm = true" class="flex-1 sm:flex-none">
                    <span class="hidden sm:inline">Tambah Pencerapan</span>
                    <span class="sm:hidden">Tambah</span>
                </UiBaseButton>
            </template>
        </UiCompositePageHeader>

        <!-- Error State -->
        <UiBaseAlert v-if="error" type="error" :message="error" />

        <!-- View Toggle -->
        <UiCompositeCard>
            <div class="flex items-center justify-center sm:justify-end">
                <div class="w-full sm:w-auto">
                    <UiBaseViewToggle v-model="viewMode" :options="viewOptions" />
                </div>
            </div>
        </UiCompositeCard>

        <!-- Add/Edit Form Modal -->
        <UiCompositeModal :is-open="showAddForm || showEditForm"
            :title="editingItem ? 'Kemaskini Pencerapan' : 'Tambah Pencerapan Baru'" @update:is-open="closeForm">
            <form @submit.prevent="handleSubmit" class="space-y-6">
                <!-- Nama Pencerap -->
                <div>
                    <UiBaseInput v-model="formData.observer_name" label="Nama Pencerap"
                        placeholder="Masukkan nama pencerap" required variant="floating" />
                </div>

                <!-- Jawatan Pencerap -->
                <div>
                    <UiBaseInput v-model="formData.observer_position" label="Jawatan"
                        placeholder="Masukkan jawatan pencerap" required variant="floating" />
                </div>

                <!-- Tarikh Pencerapan -->
                <div>
                    <UiBaseDatePicker v-model="formData.observation_date" label="Tarikh Pencerapan"
                        placeholder="Pilih tarikh pencerapan" required variant="floating" />
                </div>

                <!-- Kelas & Subjek -->
                <div>
                    <UiBaseSingleSelect v-model="formData.class_subject_id" label="Kelas & Subjek"
                        placeholder="Pilih kelas dan subjek" :options="classSubjectOptions" required variant="standard"
                        @dropdown-did-open="handleDropdownOpen" />
                </div>

                <!-- Form Actions -->
                <div
                    class="flex flex-col-reverse gap-y-2 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <UiBaseButton type="button" @click="closeForm" variant="outline">
                        Batal
                    </UiBaseButton>
                    <UiBaseButton type="submit" variant="primary" :disabled="isSubmitting">
                        <UiBaseIcon v-if="isSubmitting" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                        {{ editingItem ? 'Kemaskini' : 'Simpan' }}
                    </UiBaseButton>
                </div>
            </form>
        </UiCompositeModal>

        <!-- Card View -->
        <!-- Data Loading State for Cards -->
        <div v-if="viewMode === 'card' && dataLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="card in 6" :key="`skeleton-card-${card}`"
                class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <!-- Card Header -->
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 min-w-0 flex-1">
                            <SkeletonBox height="1.25rem" width="1.25rem" class="rounded" />
                            <SkeletonBox height="1.125rem" width="12rem" />
                        </div>
                    </div>
                </div>

                <!-- Card Content -->
                <div class="p-6 space-y-3">
                    <div v-for="field in 4" :key="`field-${field}`" class="flex items-center space-x-2">
                        <SkeletonBox height="1rem" width="1rem" class="rounded" />
                        <SkeletonBox height="0.875rem" width="4rem" variant="light" />
                        <SkeletonBox height="0.875rem" width="8rem" />
                    </div>

                    <!-- Notes field -->
                    <div class="flex items-start space-x-2">
                        <SkeletonBox height="1rem" width="1rem" class="rounded mt-0.5" />
                        <div class="min-w-0 flex-1 space-y-1">
                            <SkeletonBox height="0.875rem" width="4rem" variant="light" />
                            <SkeletonBox height="0.875rem" width="100%" variant="light" />
                            <SkeletonBox height="0.875rem" width="80%" variant="light" />
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <SkeletonBox height="1rem" width="1rem" class="rounded" />
                            <SkeletonBox height="0.875rem" width="4rem" variant="light" />
                        </div>
                        <SkeletonBox height="1.5rem" width="5rem" class="rounded-full" />
                    </div>
                </div>

                <!-- Card Footer -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-end space-x-2">
                        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                        <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Actual Card View -->
        <div v-else-if="viewMode === 'card' && !dataLoading && observationSchedules.length > 0"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <UiCompositeCard v-for="schedule in observationSchedules" :key="schedule.id"
                class="hover:shadow-lg transition-shadow">
                <template #header>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2 min-w-0 flex-1">
                            <UiBaseIcon name="heroicons:eye-solid" class="w-5 h-5 text-primary flex-shrink-0" />
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                {{ getClassSubjectLabel(schedule.class_subject_id) }}
                            </h3>
                        </div>
                    </div>
                </template>

                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <UiBaseIcon name="heroicons:user-solid" class="w-4 h-4 text-gray-500" />
                        <span class="text-sm text-gray-600 dark:text-gray-400">Pencerap:</span>
                        <span class="text-sm font-medium">{{ schedule.observer_name }}</span>
                    </div>

                    <div class="flex items-center space-x-2">
                        <UiBaseIcon name="heroicons:briefcase-solid" class="w-4 h-4 text-gray-500" />
                        <span class="text-sm text-gray-600 dark:text-gray-400">Jawatan:</span>
                        <span class="text-sm font-medium">{{ schedule.observer_position }}</span>
                    </div>

                    <div class="flex items-center space-x-2">
                        <UiBaseIcon name="heroicons:calendar-solid" class="w-4 h-4 text-gray-500" />
                        <span class="text-sm text-gray-600 dark:text-gray-400">Tarikh:</span>
                        <span class="text-sm font-medium">{{ formatDate(schedule.observation_date) }}</span>
                    </div>

                    <div class="flex items-start space-x-2">
                        <UiBaseIcon name="heroicons:document-text-solid" class="w-4 h-4 text-gray-500 mt-0.5" />
                        <div class="min-w-0 flex-1">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Catatan:</span>
                            <p class="text-sm text-gray-700 dark:text-gray-300 mt-1">{{ getNotesText(schedule.notes) }}
                            </p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <UiBaseIcon name="heroicons:flag-solid" class="w-4 h-4 text-gray-500" />
                            <span class="text-sm text-gray-600 dark:text-gray-400">Status:</span>
                        </div>
                        <span :class="getStatusBadgeClass(schedule.status)"
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                            {{ getStatusLabel(schedule.status) }}
                        </span>
                    </div>
                </div>

                <template #footer>
                    <div class="flex items-center justify-end space-x-2">
                        <UiBaseButton @click="printSchedule(schedule)" variant="outline" size="sm"
                            prepend-icon="heroicons:printer-solid">
                            Cetak
                        </UiBaseButton>
                        <UiBaseButton @click="editSchedule(schedule)" variant="secondary" size="sm"
                            prepend-icon="heroicons:pencil-solid">
                            Edit
                        </UiBaseButton>
                        <UiBaseButton @click="deleteSchedule(schedule)" variant="alert-error" size="sm"
                            prepend-icon="heroicons:trash-solid">
                            Padam
                        </UiBaseButton>
                    </div>
                </template>
            </UiCompositeCard>
        </div>

        <!-- Table View -->
        <!-- Data Loading State for Table -->
        <div v-if="viewMode === 'table' && dataLoading"
            class="bg-white dark:bg-dark-card rounded-lg shadow-sm border border-gray-200 dark:border-dark-border overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <SkeletonBox height="0.875rem" width="4rem" />
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <SkeletonBox height="0.875rem" width="5rem" />
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <SkeletonBox height="0.875rem" width="5rem" />
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <SkeletonBox height="0.875rem" width="8rem" />
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <SkeletonBox height="0.875rem" width="5rem" />
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <SkeletonBox height="0.875rem" width="4rem" />
                            </th>
                            <th
                                class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                <SkeletonBox height="0.875rem" width="6rem" />
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="row in 6" :key="`skeleton-row-${row}`"
                            class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <SkeletonBox height="0.875rem" width="6rem" />
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <SkeletonBox height="0.875rem" width="8rem" />
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <SkeletonBox height="0.875rem" width="10rem" />
                            </td>
                            <td class="px-6 py-4">
                                <SkeletonBox height="0.875rem" width="12rem" />
                            </td>
                            <td class="px-6 py-4">
                                <div class="space-y-1">
                                    <SkeletonBox height="0.875rem" width="100%" />
                                    <SkeletonBox height="0.875rem" width="80%" />
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <SkeletonBox height="1.5rem" width="5rem" class="rounded-full" />
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right">
                                <div class="flex items-center justify-end space-x-2">
                                    <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                                    <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                                    <SkeletonBox height="2rem" width="4rem" class="rounded-md" />
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Actual Table View -->
        <div v-else-if="viewMode === 'table' && !dataLoading && observationSchedules.length > 0"
            class="bg-white dark:bg-dark-card rounded-lg shadow-sm border border-gray-200 dark:border-dark-border overflow-hidden">
            <div class="overflow-x-auto" ref="tableContainer">
                <table class="w-full">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Tarikh
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Pencerap
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Jawatan
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Kelas & Subjek
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Catatan
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Status
                            </th>
                            <th
                                class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Tindakan
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="schedule in observationSchedules" :key="schedule.id"
                            class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ formatDate(schedule.observation_date) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ schedule.observer_name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ schedule.observer_position }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                                <div class="max-w-xs truncate" :title="getClassSubjectLabel(schedule.class_subject_id)">
                                    {{ getClassSubjectLabel(schedule.class_subject_id) }}
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                                <div class="max-w-xs truncate" :title="schedule.notes || 'Tiada catatan'">
                                    {{ schedule.notes || 'Tiada catatan' }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {{ getStatusLabel(schedule.status) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="relative">
                                    <UiBaseButton variant="flat" size="sm" class="p-2"
                                        @click="toggleDropdown(schedule.id)" data-dropdown-trigger>
                                        <UiBaseIcon name="heroicons:ellipsis-vertical-solid" class="w-4 h-4" />
                                    </UiBaseButton>
                                    <div v-if="openDropdownId === schedule.id"
                                        class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50"
                                        ref="dropdownMenu">
                                        <button @click="printSchedule(schedule); closeDropdown()"
                                            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                            <UiBaseIcon name="heroicons:printer-solid" class="w-4 h-4 mr-3" />
                                            Cetak
                                        </button>
                                        <button @click="editSchedule(schedule); closeDropdown()"
                                            class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                            <UiBaseIcon name="heroicons:pencil-solid" class="w-4 h-4 mr-3" />
                                            Edit
                                        </button>
                                        <button @click="deleteSchedule(schedule); closeDropdown()"
                                            class="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                            <UiBaseIcon name="heroicons:trash-solid" class="w-4 h-4 mr-3" />
                                            Padam
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Empty State -->
        <div v-else-if="!loading && !dataLoading && observationSchedules.length === 0" class="text-center py-12">
            <UiBaseIcon name="heroicons:eye-slash-solid" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Tiada Jadual Pencerapan</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">Belum ada jadual pencerapan yang dijadualkan.</p>
            <UiBaseButton @click="showAddForm = true" variant="primary" prepend-icon="heroicons:plus-solid">
                Tambah Pencerapan Pertama
            </UiBaseButton>
        </div>

        <!-- Delete Confirmation Modal -->
        <UiCompositeDeleteConfirmationModal :is-open="showDeleteModal" item-type="Jadual Pencerapan"
            :item-name="deletingItem?.observer_name" :item-subtitle="deleteModalSubtitle" @confirm="confirmDelete"
            @cancel="showDeleteModal = false" />
    </div>

    <!-- Print View (Hidden) -->
    <div id="print-view" class="hidden print:block">
        <div class="p-8">
            <div class="text-center mb-8">
                <h1 class="text-2xl font-bold mb-4">JADUAL PENCERAPAN PDPC GURU</h1>
                <p class="text-lg font-semibold mb-6">{{ userProfile?.full_name || 'Nama Guru' }}</p>
            </div>

            <table class="w-full border-collapse border border-gray-300">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="border border-gray-300 px-4 py-2 text-left">Nama Pencerap</th>
                        <th class="border border-gray-300 px-4 py-2 text-left">Jawatan</th>
                        <th class="border border-gray-300 px-4 py-2 text-left">Tarikh</th>
                        <th class="border border-gray-300 px-4 py-2 text-left">Kelas & Subjek</th>
                        <th class="border border-gray-300 px-4 py-2 text-left">Catatan</th>
                        <th class="border border-gray-300 px-4 py-2 text-left">Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="schedule in observationSchedules" :key="schedule.id">
                        <td class="border border-gray-300 px-4 py-2">{{ schedule.observer_name }}</td>
                        <td class="border border-gray-300 px-4 py-2">{{ schedule.observer_position }}</td>
                        <td class="border border-gray-300 px-4 py-2">{{ formatDate(schedule.observation_date) }}</td>
                        <td class="border border-gray-300 px-4 py-2">{{ getClassSubjectLabel(schedule.class_subject_id)
                            }}
                        </td>
                        <td class="border border-gray-300 px-4 py-2">{{ schedule.notes || 'Tiada catatan' }}</td>
                        <td class="border border-gray-300 px-4 py-2">{{ getStatusLabel(schedule.status) }}</td>
                    </tr>
                    <tr v-if="observationSchedules.length === 0">
                        <td colspan="6" class="border border-gray-300 px-4 py-4 text-center text-gray-500">
                            Tiada jadual pencerapan
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useSupabaseClient, useSupabaseUser } from '#imports'
import { useSubjects } from '~/composables/useSubjects'
import { useToast } from '~/composables/useToast'
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'
import type { Database } from '~/types/supabase'
import SkeletonLoader from '~/components/ui/skeleton/SkeletonLoader.vue'
import SkeletonPageHeader from '~/components/ui/skeleton/SkeletonPageHeader.vue'
import SkeletonTable from '~/components/ui/skeleton/SkeletonTable.vue'
import SkeletonBox from '~/components/ui/skeleton/SkeletonBox.vue'

// Components - using standardized UI components
// No need to import individual components as they're auto-imported with Ui prefix

// Page meta
definePageMeta({
    layout: 'default'
})

// SEO
useHead({
    title: 'Jadual Pencerapan PDPC Guru - EduPlan Pro',
    meta: [
        { name: 'description', content: 'Urus jadual pencerapan pengajaran dan pembelajaran untuk pemantauan kualiti.' }
    ]
})

// Types
interface ObservationSchedule {
    id: string
    user_id: string
    observer_name: string
    observer_position: string
    observation_date: string
    class_subject_id: string
    notes?: string | null
    status: string
    created_at: string
    updated_at: string
}

interface FormData {
    observer_name: string
    observer_position: string
    observation_date: string
    class_subject_id: string
}

// Composables
const supabase = useSupabaseClient<Database>()
const user = useSupabaseUser()
const { subjects, fetchSubjects } = useSubjects()
const { success: showSuccessToast, error: showErrorToast } = useToast()

// State
const loading = ref(true)
const dataLoading = ref(false)
const error = ref<string | null>(null)
const observationSchedules = ref<ObservationSchedule[]>([])
const userProfile = ref<any>(null)
const userClassSubjects = ref<UserClassSubjectEntry[]>([])

// Form state
const showAddForm = ref(false)
const showEditForm = ref(false)
const isSubmitting = ref(false)
const editingItem = ref<ObservationSchedule | null>(null)

// Delete state
const showDeleteModal = ref(false)
const deletingItem = ref<ObservationSchedule | null>(null)

// View mode state
const viewMode = ref<'card' | 'table'>('card')
const viewOptions = [
    { value: 'card', label: 'Kad', icon: 'heroicons:rectangle-stack' },
    { value: 'table', label: 'Senarai', icon: 'heroicons:table-cells' }
]

// Dropdown state
const openDropdownId = ref<string | null>(null)
const tableContainer = ref<HTMLElement | null>(null)
const dropdownMenu = ref<HTMLElement | null>(null)

// Computed properties
const deleteModalSubtitle = computed(() => {
    if (!deletingItem.value) return ''
    return `${getClassSubjectLabel(deletingItem.value.class_subject_id)} - ${formatDate(deletingItem.value.observation_date)}`
})

// Form data
const formData = ref<FormData>({
    observer_name: '',
    observer_position: '',
    observation_date: '',
    class_subject_id: ''
})

// Computed
const classSubjectOptions = computed(() => {
    return userClassSubjects.value.map(cs => {
        const subject = subjects.value.find(s => s.id === cs.subject_id)
        const compositeId = `${cs.class_id}_${cs.subject_id}`
        return {
            value: compositeId,
            label: `${cs.className} - ${subject?.name || 'Unknown Subject'}`
        }
    })
})

// Methods
const fetchUserProfile = async () => {
    if (!user.value) return

    try {
        const { data, error: fetchError } = await supabase
            .from('profiles')
            .select('full_name, class_subjects')
            .eq('id', user.value.id)
            .single()

        if (fetchError) throw fetchError

        userProfile.value = data
        userClassSubjects.value = (data?.class_subjects as UserClassSubjectEntry[]) || []
    } catch (err) {
        console.error('Error fetching user profile:', err)
        error.value = 'Gagal memuatkan profil pengguna'
    }
}

const fetchObservationSchedules = async () => {
    if (!user.value) return

    try {
        dataLoading.value = true
        const { data, error: fetchError } = await supabase
            .from('observation_schedules')
            .select('*')
            .eq('user_id', user.value.id)
            .order('observation_date', { ascending: true })

        if (fetchError) throw fetchError

        observationSchedules.value = data || []
    } catch (err) {
        console.error('Error fetching observation schedules:', err)
        error.value = 'Gagal memuatkan jadual pencerapan'
    } finally {
        dataLoading.value = false
    }
}

const getClassSubjectLabel = (classSubjectId: string) => {
    // classSubjectId is in format "class_id_subject_id"
    const [classId, subjectId] = classSubjectId.split('_')
    const cs = userClassSubjects.value.find(item =>
        item.class_id === classId && item.subject_id === subjectId
    )
    if (!cs) return 'Kelas & Subjek Tidak Diketahui'

    const subject = subjects.value.find(s => s.id === cs.subject_id)
    return `${cs.className} - ${subject?.name || 'Unknown Subject'}`
}

const getStatusLabel = (status: string) => {
    switch (status) {
        case 'dijadualkan': return 'Dijadualkan'
        case 'selesai': return 'Selesai'
        case 'dibatalkan': return 'Dibatalkan'
        case 'dijadualkan semula': return 'Dijadualkan Semula'
        default: return status
    }
}

const getStatusBadgeClass = (status: string) => {
    switch (status) {
        case 'dijadualkan':
            return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
        case 'selesai':
            return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        case 'dibatalkan':
            return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        case 'dijadualkan semula':
            return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        default:
            return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
    }
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ms-MY', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    })
}

const getNotesText = (notes: string | null | undefined) => {
    return notes || 'Tiada catatan'
}

const resetForm = () => {
    formData.value = {
        observer_name: '',
        observer_position: '',
        observation_date: '',
        class_subject_id: ''
    }
    editingItem.value = null
}

const closeForm = () => {
    showAddForm.value = false
    showEditForm.value = false
    resetForm()
}

const editSchedule = (schedule: ObservationSchedule) => {
    editingItem.value = schedule
    formData.value = {
        observer_name: schedule.observer_name,
        observer_position: schedule.observer_position,
        observation_date: schedule.observation_date,
        class_subject_id: schedule.class_subject_id
    }
    showEditForm.value = true
}

const deleteSchedule = (schedule: ObservationSchedule) => {
    deletingItem.value = schedule
    showDeleteModal.value = true
}

const handleSubmit = async () => {
    if (!user.value) return

    try {
        isSubmitting.value = true
        error.value = null

        const scheduleData = {
            user_id: user.value.id,
            observer_name: formData.value.observer_name,
            observer_position: formData.value.observer_position,
            observation_date: formData.value.observation_date,
            class_subject_id: formData.value.class_subject_id,
            updated_at: new Date().toISOString()
        }

        if (editingItem.value) {
            // Update existing schedule
            const { error: updateError } = await supabase
                .from('observation_schedules')
                .update(scheduleData)
                .eq('id', editingItem.value.id)

            if (updateError) throw updateError
        } else {
            // Create new schedule
            const { error: insertError } = await supabase
                .from('observation_schedules')
                .insert(scheduleData)

            if (insertError) throw insertError
        }

        await fetchObservationSchedules()
        closeForm()
        showSuccessToast(editingItem.value ? 'Jadual pencerapan berjaya dikemaskini' : 'Jadual pencerapan berjaya ditambah')
    } catch (err) {
        console.error('Error saving observation schedule:', err)
        const errorMessage = 'Gagal menyimpan jadual pencerapan'
        error.value = errorMessage
        showErrorToast(errorMessage)
    } finally {
        isSubmitting.value = false
    }
}

const confirmDelete = async () => {
    if (!deletingItem.value) return

    try {
        const { error: deleteError } = await supabase
            .from('observation_schedules')
            .delete()
            .eq('id', deletingItem.value.id)

        if (deleteError) throw deleteError

        await fetchObservationSchedules()
        showDeleteModal.value = false
        deletingItem.value = null
        showSuccessToast('Jadual pencerapan berjaya dipadam')
    } catch (err) {
        console.error('Error deleting observation schedule:', err)
        const errorMessage = 'Gagal memadam jadual pencerapan'
        error.value = errorMessage
        showErrorToast(errorMessage)
    }
}

const printSchedule = (schedule: ObservationSchedule) => {
    // Create a new window for printing individual schedule
    const printWindow = window.open('', '_blank')
    if (!printWindow) return

    const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Jadual Pencerapan PDPC - ${schedule.observer_name}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    font-size: 14px;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .title {
                    font-size: 20px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                .subtitle {
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
                .content {
                    max-width: 100%;
                    margin: 0 auto;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }
                th, td {
                    border: 1px solid #333;
                    padding: 8px 12px;
                    text-align: left;
                    vertical-align: top;
                }
                th {
                    background-color: #f5f5f5;
                    font-weight: bold;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                }
                @media print {
                    body { margin: 15px; }
                    .no-print { display: none; }
                    @page {
                        size: A4;
                        margin: 15mm;
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">JADUAL PENCERAPAN PDPC GURU</div>
                <div class="subtitle">${userProfile.value?.full_name || 'Nama Guru'}</div>
            </div>

            <div class="content">
                <table>
                    <thead>
                        <tr>
                            <th>Nama Pencerap</th>
                            <th>Jawatan</th>
                            <th>Tarikh</th>
                            <th>Kelas & Subjek</th>
                            <th>Catatan</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>${schedule.observer_name}</td>
                            <td>${schedule.observer_position}</td>
                            <td>${formatDate(schedule.observation_date)}</td>
                            <td>${getClassSubjectLabel(schedule.class_subject_id)}</td>
                            <td>${schedule.notes || 'Tiada catatan'}</td>
                            <td>${getStatusLabel(schedule.status)}</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="footer">
                Dicetak pada: ${new Date().toLocaleDateString('ms-MY', { day: 'numeric', month: 'long', year: 'numeric' })}
            </div>

            <script>
                window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                        window.close();
                    }
                }
            <\/script>
        </body>
        </html>`

    printWindow.document.write(printContent)
    printWindow.document.close()
}



// Dropdown functions
const toggleDropdown = async (scheduleId: string) => {
    openDropdownId.value = openDropdownId.value === scheduleId ? null : scheduleId

    // If dropdown is opened, check if auto-scroll is needed
    if (openDropdownId.value === scheduleId) {
        await nextTick()
        checkAndScrollToDropdown()
    }
}

const closeDropdown = () => {
    openDropdownId.value = null
}

// Check if dropdown extends beyond table area and auto-scroll if needed
const checkAndScrollToDropdown = () => {
    if (!tableContainer.value || !dropdownMenu.value) return

    const tableRect = tableContainer.value.getBoundingClientRect()
    const dropdownRect = dropdownMenu.value.getBoundingClientRect()

    // Check if dropdown extends beyond the bottom of the table container
    if (dropdownRect.bottom > tableRect.bottom) {
        // Check if scrollbar appeared by comparing scrollHeight with clientHeight
        const hasScrollbar = tableContainer.value.scrollHeight > tableContainer.value.clientHeight

        if (hasScrollbar) {
            // Scroll to the bottom of the table smoothly
            tableContainer.value.scrollTo({
                top: tableContainer.value.scrollHeight,
                behavior: 'smooth'
            })
        }
    }
}

// Handle outside click to close dropdown
const handleOutsideClick = (event: Event) => {
    if (!dropdownMenu.value) return

    const target = event.target as HTMLElement
    if (!dropdownMenu.value.contains(target) && !target.closest('[data-dropdown-trigger]')) {
        closeDropdown()
    }
}

// Handle dropdown opening to auto-scroll modal content
const handleDropdownOpen = async () => {
    await nextTick()

    // Find the modal content container
    const selectors = [
        '.modal-content',
        '[role="dialog"] .overflow-y-auto',
        '.overflow-y-auto',
        '[data-scroll-container]'
    ]

    let scrollContainer = null
    for (const selector of selectors) {
        scrollContainer = document.querySelector(selector)
        if (scrollContainer) break
    }

    // Fallback: find any scrollable parent of the form
    if (!scrollContainer) {
        const form = document.querySelector('form')
        if (form) {
            let parent = form.parentElement
            while (parent && parent !== document.body) {
                const style = window.getComputedStyle(parent)
                if (style.overflowY === 'auto' || style.overflowY === 'scroll') {
                    scrollContainer = parent
                    break
                }
                parent = parent.parentElement
            }
        }
    }

    if (scrollContainer) {
        // Check if dropdown extends beyond visible area
        const containerRect = scrollContainer.getBoundingClientRect()
        const containerBottom = containerRect.bottom

        // Find the dropdown element
        const dropdown = document.querySelector('[role="listbox"]')
        if (dropdown) {
            const dropdownRect = dropdown.getBoundingClientRect()
            const dropdownBottom = dropdownRect.bottom

            // If dropdown extends beyond container, scroll to bottom
            if (dropdownBottom > containerBottom) {
                scrollContainer.scrollTo({
                    top: scrollContainer.scrollHeight,
                    behavior: 'smooth'
                })
            }
        }
    }
}

// Lifecycle
onMounted(async () => {
    try {
        loading.value = true
        await Promise.all([
            fetchUserProfile(),
            fetchObservationSchedules(),
            fetchSubjects()
        ])
    } catch (err) {
        console.error('Error loading page data:', err)
        error.value = 'Gagal memuatkan data'
    } finally {
        loading.value = false
    }

    // Add event listener for outside clicks
    document.addEventListener('click', handleOutsideClick)
})

onUnmounted(() => {
    // Remove event listener
    document.removeEventListener('click', handleOutsideClick)
})
</script>

<style scoped>
@media print {
    body * {
        visibility: hidden;
    }

    #print-view,
    #print-view * {
        visibility: visible;
    }

    #print-view {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    .no-print {
        display: none !important;
    }
}
</style>
