<template>
    <UiCompositeModal :is-open="isOpen" :title="event?.title || 'Butiran <PERSON>'" size="lg"
        @update:is-open="handleClose">
        <div v-if="event" class="space-y-6">
            <!-- Event Header -->
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-4">
                    <div class="w-12 h-12 rounded-lg flex items-center justify-center"
                        :class="getCategoryConfig(event.category).bgColor">
                        <UiBaseIcon :name="getCategoryConfig(event.category).icon" class="w-6 h-6"
                            :class="getCategoryConfig(event.category).textColor" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                            {{ event.title }}
                        </h2>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                :class="getCategoryConfig(event.category).bgColor + ' ' + getCategoryConfig(event.category).textColor">
                                {{ getCategoryConfig(event.category).label }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Description -->
            <div v-if="event.description" class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Penerangan</h3>
                <p class="text-gray-900 dark:text-white whitespace-pre-wrap">{{ event.description }}</p>
            </div>

            <!-- Event Details Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Date and Time -->
                <div class="space-y-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Tarikh & Masa</h3>

                    <!-- Start Date -->
                    <div class="flex items-center space-x-3">
                        <UiBaseIcon name="heroicons:calendar-solid" class="w-5 h-5 text-gray-400" />
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">Tarikh Mula</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {{ formatDate(event.start_date) }}
                            </p>
                        </div>
                    </div>

                    <!-- End Date (if different) -->
                    <div v-if="event.end_date && event.end_date !== event.start_date"
                        class="flex items-center space-x-3">
                        <UiBaseIcon name="heroicons:calendar-solid" class="w-5 h-5 text-gray-400" />
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">Tarikh Akhir</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {{ formatDate(event.end_date) }}
                            </p>
                        </div>
                    </div>


                </div>

                <!-- Location and Additional Info -->
                <div class="space-y-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">Maklumat Tambahan</h3>

                    <!-- Location -->
                    <div v-if="event.location" class="flex items-center space-x-3">
                        <UiBaseIcon name="heroicons:map-pin-solid" class="w-5 h-5 text-gray-400" />
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">Lokasi</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ event.location }}</p>
                        </div>
                    </div>



                    <!-- Created Date -->
                    <div class="flex items-center space-x-3">
                        <UiBaseIcon name="heroicons:document-plus-solid" class="w-5 h-5 text-gray-400" />
                        <div>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">Dicipta</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                {{ formatDate(event.created_at) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>


            <div class="text-sm text-gray-500 dark:text-gray-400">
                Kemaskini terakhir: {{ event ? formatDate(event.updated_at) : '' }}
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end">

                <div class="flex space-x-3">
                    <UiBaseButton variant="outline" @click="handleClose">
                        Tutup
                    </UiBaseButton>
                    <UiBaseButton variant="primary" prepend-icon="heroicons:pencil-solid" @click="handleEdit">
                        Kemaskini
                    </UiBaseButton>
                    <UiBaseButton variant="delete" prepend-icon="heroicons:trash-solid" @click="handleDelete">
                        Padam
                    </UiBaseButton>
                </div>
            </div>
        </template>
    </UiCompositeModal>
</template>

<script setup lang="ts">
import type { CalendarEvent } from '~/types/calendar'
import { getCategoryConfig } from '~/types/calendar'

// Props
interface Props {
    isOpen: boolean
    event?: CalendarEvent | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
    'close': []
    'edit': [event: CalendarEvent]
    'delete': [event: CalendarEvent]
}>()

// Methods
const handleClose = () => {
    emit('close')
}

const handleEdit = () => {
    if (props.event) {
        emit('edit', props.event)
    }
}

const handleDelete = () => {
    if (props.event) {
        emit('delete', props.event)
    }
}

const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ms-MY', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    })
}


</script>

<style scoped>
/* Add any component-specific styles here */
</style>
