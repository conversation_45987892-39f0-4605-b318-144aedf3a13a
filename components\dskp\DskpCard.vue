<template>
    <div
        class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200">
        <!-- Header -->
        <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                    {{ dskpStatus.subject_name }}
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    {{ dskpStatus.class_name }}
                </p>
            </div>

            <!-- Status Badge -->
            <div class="flex-shrink-0">
                <span v-if="dskpStatus.has_dskp"
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    <Icon name="heroicons:check-circle-solid" class="w-3 h-3 mr-1" />
                    Lengkap
                </span>
                <span v-else
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                    <Icon name="heroicons:clock-solid" class="w-3 h-3 mr-1" />
                    Belum Muat Naik
                </span>
            </div>
        </div>

        <!-- File Information (if DSKP exists) -->
        <div v-if="dskpStatus.has_dskp && dskpStatus.dskp_document" class="mb-4">
            <div
                class="border-2 border-dashed border-gray-300 dark:border-gray-600 text-center p-6 bg-gray-50 dark:bg-gray-700 rounded-md">
                <div class="flex-shrink-0">
                    <Icon :name="getFileTypeIcon(dskpStatus.dskp_document.file_mime_type)"
                        class="w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {{ dskpStatus.dskp_document.file_name }}
                    </p>
                    <div class="items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>{{ formatFileSize(dskpStatus.dskp_document.file_size_bytes) }}</span>
                        <span>{{ formatDate(dskpStatus.dskp_document.updated_at) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Area (if no DSKP) -->
        <div v-else class="mb-4">
            <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md p-6 text-center">
                <Icon name="heroicons:document-arrow-up" class="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Belum ada DSKP untuk kelas dan subjek ini
                </p>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between">
            <div class="flex space-x-2">
                <!-- Preview Button (if DSKP exists) -->
                <UiBaseButton v-if="dskpStatus.has_dskp" variant="outline" size="sm" :disabled="isLoading"
                    prepend-icon="heroicons:eye" @click="handlePreview">
                    Pratonton
                </UiBaseButton>

                <!-- Upload/Replace Button -->
                <UiBaseButton :variant="dskpStatus.has_dskp ? 'outline' : 'primary'" size="sm" :disabled="isLoading"
                    :prepend-icon="dskpStatus.has_dskp ? 'heroicons:arrow-path' : 'heroicons:arrow-up-tray'"
                    @click="triggerFileUpload">
                    {{ dskpStatus.has_dskp ? 'Ganti' : 'Muat Naik' }}
                </UiBaseButton>
            </div>

            <!-- Delete Button (if DSKP exists) -->
            <UiBaseButton v-if="dskpStatus.has_dskp" variant="alert-error" size="sm" :disabled="isLoading"
                @click="handleDelete">
                <Icon name="heroicons:trash" class="w-4 h-4" />
            </UiBaseButton>
        </div>

        <!-- Hidden File Input -->
        <input ref="fileInputRef" type="file" class="hidden" :accept="acceptedFileTypes" @change="handleFileSelect" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import type { DskpStatus, DskpDocument } from '~/types/dskpDocuments';
import { getFileTypeIcon, formatFileSize } from '~/types/dskpDocuments';
import UiBaseButton from '~/components/ui/base/Button.vue';
import Icon from '~/components/ui/base/Icon.vue';

interface Props {
    dskpStatus: DskpStatus;
    isLoading?: boolean;
}

interface Emits {
    (e: 'upload', classId: string, subjectId: string, file: File): void;
    (e: 'replace', dskpDocument: DskpDocument, file: File): void;
    (e: 'preview', dskpDocument: DskpDocument): void;
    (e: 'delete', dskpDocument: DskpDocument): void;
}

const props = withDefaults(defineProps<Props>(), {
    isLoading: false,
});

const emit = defineEmits<Emits>();

// Refs
const fileInputRef = ref<HTMLInputElement | null>(null);

// Computed
const acceptedFileTypes = computed(() => {
    return '.pdf,.docx,.xlsx,.pptx,.doc,.xls,.ppt';
});

// Methods
const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ms-MY', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
    });
};

const triggerFileUpload = (): void => {
    fileInputRef.value?.click();
};

const handleFileSelect = (event: Event): void => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];

    if (!file) return;

    if (props.dskpStatus.has_dskp && props.dskpStatus.dskp_document) {
        // Replace existing DSKP
        emit('replace', props.dskpStatus.dskp_document, file);
    } else {
        // Upload new DSKP
        if (props.dskpStatus.subject_id) {
            emit('upload', props.dskpStatus.class_id, props.dskpStatus.subject_id, file);
        } else {
            console.error('Cannot upload DSKP: subject_id is null');
        }
    }

    // Reset file input
    target.value = '';
};

const handlePreview = (): void => {
    if (props.dskpStatus.dskp_document) {
        emit('preview', props.dskpStatus.dskp_document);
    }
};

const handleDelete = (): void => {
    if (props.dskpStatus.dskp_document) {
        emit('delete', props.dskpStatus.dskp_document);
    }
};
</script>
