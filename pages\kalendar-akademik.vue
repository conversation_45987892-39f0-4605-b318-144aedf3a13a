<template>
  <!-- Page Loading State -->
  <SkeletonLoader v-if="loading" variant="kalendar-akademik" />

  <div v-if="!loading" class="space-y-8">
    <!-- Page Header -->
    <UiCompositePageHeader title="Kalendar Akademik" subtitle="Urus dokumen kalendar akademik anda"
      icon="heroicons:calendar-days-solid" />

    <!-- Main Content -->
    <div class="space-y-8">

      <!-- Document Display Section -->
      <div v-if="academicCalendarStatus.hasDocument && documentDisplayUrl" class="space-y-4">
        <UiCompositeCard>
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <UiBaseIcon name="heroicons:eye-solid" class="w-5 h-5 text-primary" />
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Pratonton Dokumen</h3>
              </div>
              <a :href="newTabUrl" target="_blank" rel="noopener noreferrer" v-if="newTabUrl">
                <UiBaseButton variant="outline" size="sm" prepend-icon="heroicons:arrow-top-right-on-square">
                  Buka di Tab Baharu
                </UiBaseButton>
              </a>
            </div>
          </template>

          <!-- Document Viewer -->
          <div class="relative">
            <!-- Loading State -->
            <div v-if="isDocumentLoading" class="flex justify-center items-center h-96">
              <div class="text-center">
                <UiBaseIcon name="svg-spinners:270-ring-with-bg" class="h-12 w-12 text-primary mx-auto" />
                <p class="mt-2 text-gray-600 dark:text-gray-300">Memuatkan dokumen...</p>
              </div>
            </div>

            <!-- Document Content -->
            <div v-else-if="documentDisplayType === 'office'" class="w-full">
              <iframe :src="documentDisplayUrl" width="100%" height="900" frameborder="0"
                class="rounded-md border border-gray-200 dark:border-gray-700">
                Memuatkan pratonton dokumen...
              </iframe>
            </div>

            <div v-else-if="documentDisplayType === 'image'" class="flex items-center justify-center p-4">
              <img :src="documentDisplayUrl" :alt="academicCalendarStatus.document!.file_name"
                class="max-w-full max-h-96 object-contain rounded-md shadow-md" />
            </div>

            <div v-else class="flex items-center justify-center h-48">
              <p class="text-center text-gray-500">Gagal memuatkan pratonton dokumen.</p>
            </div>
          </div>
        </UiCompositeCard>
      </div>

      <!-- Upload/Document Management Section -->
      <UiCompositeCard>
        <template #header>
          <div class="flex items-center space-x-3">
            <UiBaseIcon name="heroicons:document-arrow-up-solid" class="w-5 h-5 text-primary" />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ academicCalendarStatus.hasDocument ? 'Dokumen Kalendar Akademik' : 'Muat Naik Kalendar Akademik' }}
            </h3>
          </div>
        </template>

        <!-- No Document State - Upload Area -->
        <div v-if="!academicCalendarStatus.hasDocument" class="space-y-6">
          <div class="text-center">
            <UiBaseIcon name="heroicons:calendar-days" class="mx-auto h-16 w-16 text-gray-400" />
            <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">Tiada kalendar akademik</h3>
            <p class="mt-1 text-gray-500 dark:text-gray-400">
              Muat naik dokumen kalendar akademik anda.
            </p>
          </div>

          <!-- File Upload Component -->
          <div class="space-y-4">
            <FileUpload v-model="selectedFile" :max-file-size-mb="10"
              accept=".pdf,.docx,.xlsx,.pptx,.doc,.xls,.ppt,.jpg,.jpeg,.png,.gif,.webp"
              input-id="academicCalendarUpload" @file-error="handleFileError" />

            <!-- Upload Button -->
            <div class="flex justify-center">
              <UiBaseButton variant="primary" size="md" prepend-icon="heroicons:cloud-arrow-up-solid"
                :disabled="!selectedFile || isUploading" :loading="isUploading" @click="handleUpload">
                {{ isUploading ? 'Memuat naik...' : 'Muat Naik Kalendar Akademik' }}
              </UiBaseButton>
            </div>
          </div>

          <!-- MOE Info Message -->
          <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-start space-x-3">
              <UiBaseIcon name="heroicons:information-circle-solid"
                class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
              <div class="text-sm text-blue-700 dark:text-blue-300">
                <p class="font-medium mb-1">Cadangan:</p>
                <p>
                  Muat Turun Kalendar Akademik dari
                  <a href="https://www.moe.gov.my/kalendar-akademik" target="_blank" rel="noopener noreferrer"
                    class="font-medium underline hover:no-underline">
                    https://www.moe.gov.my/kalendar-akademik
                  </a>
                  dan Muat Naik ke laman ini.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Document Exists State - Document Display and Actions -->
        <div v-else class="space-y-6">
          <!-- Document Info -->
          <div class="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex-shrink-0">
              <UiBaseIcon :name="getFileTypeIcon(academicCalendarStatus.document!.file_mime_type)"
                class="w-8 h-8 text-primary" />
            </div>
            <div class="flex-1 min-w-0">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                {{ academicCalendarStatus.document!.file_name }}
              </h4>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ getFileTypeDisplayName(academicCalendarStatus.document!.file_mime_type) }} •
                {{ formatFileSize(academicCalendarStatus.document!.file_size_bytes) }}
              </p>
              <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Dimuat naik: {{ formatDate(academicCalendarStatus.document!.created_at) }}
              </p>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-3">
            <UiBaseButton variant="outline" size="md" prepend-icon="heroicons:arrow-path-solid"
              @click="showReplaceModal = true" class="flex-1">
              Ganti
            </UiBaseButton>
            <UiBaseButton variant="alert-error" size="md" prepend-icon="heroicons:trash-solid"
              @click="showDeleteModal = true" class="flex-1">
              Padam
            </UiBaseButton>
          </div>

          <!-- MOE Info Message -->
          <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex items-start space-x-3">
              <UiBaseIcon name="heroicons:information-circle-solid"
                class="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
              <div class="text-sm text-blue-700 dark:text-blue-300">
                <p class="font-medium mb-1">Cadangan:</p>
                <p>
                  Muat Turun Kalendar Akademik dari
                  <a href="https://www.moe.gov.my/kalendar-akademik" target="_blank" rel="noopener noreferrer"
                    class="font-medium underline hover:no-underline">
                    https://www.moe.gov.my/kalendar-akademik
                  </a>
                  dan Muat Naik ke laman ini.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Display -->
        <div v-if="error" class="mt-4">
          <UiBaseAlert variant="error" :title="error" />
        </div>
      </UiCompositeCard>
    </div>

    <!-- Replace Modal -->
    <UiCompositeModal :is-open="showReplaceModal" title="Ganti Kalendar Akademik" @close="closeReplaceModal" size="lg">
      <div class="space-y-6">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          <p>Pilih fail baharu untuk menggantikan kalendar akademik sedia ada.</p>
          <p class="mt-2 font-medium text-amber-600 dark:text-amber-400">
            Amaran: Fail lama akan dipadam secara kekal.
          </p>
        </div>

        <FileUpload v-model="replaceFile" :max-file-size-mb="10"
          accept=".pdf,.docx,.xlsx,.pptx,.doc,.xls,.ppt,.jpg,.jpeg,.png,.gif,.webp" input-id="replaceFileUpload"
          @file-error="handleFileError" />

        <div class="flex justify-end space-x-3">
          <UiBaseButton variant="outline" @click="closeReplaceModal" :disabled="isReplacing">
            Batal
          </UiBaseButton>
          <UiBaseButton variant="primary" :disabled="!replaceFile || isReplacing" :loading="isReplacing"
            @click="handleReplace">
            {{ isReplacing ? 'Mengganti...' : 'Ganti Dokumen' }}
          </UiBaseButton>
        </div>
      </div>
    </UiCompositeModal>

    <!-- Delete Confirmation Modal -->
    <UiCompositeDeleteConfirmationModal :is-open="showDeleteModal" title="Padam Kalendar Akademik"
      :confirmation-message="`Adakah anda pasti ingin memadam dokumen '${academicCalendarStatus.document?.file_name}'?`"
      :item-name="academicCalendarStatus.document?.file_name" confirm-text="Padam" :loading="isDeleting"
      @confirm="handleDelete" @cancel="showDeleteModal = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useToast } from '~/composables/useToast';
import { useAcademicCalendarDocuments } from '~/composables/useAcademicCalendarDocuments';
import {
  getFileTypeIcon,
  getFileTypeDisplayName,
  formatFileSize,
  getPreviewType
} from '~/types/academicCalendarDocuments';

// Components
import SkeletonLoader from '~/components/ui/skeleton/SkeletonLoader.vue';
import UiCompositePageHeader from '~/components/ui/composite/PageHeader.vue';
import UiCompositeCard from '~/components/ui/composite/Card.vue';
import UiCompositeModal from '~/components/ui/composite/Modal.vue';
import UiCompositeDeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue';
import UiBaseIcon from '~/components/ui/base/Icon.vue';
import UiBaseButton from '~/components/ui/base/Button.vue';
import UiBaseAlert from '~/components/ui/base/Alert.vue';
import FileUpload from '~/components/rph/FileUpload.vue';

// Define page meta
definePageMeta({
  layout: 'default'
});

// Set page head
useHead({
  title: 'Kalendar Akademik - EduPlan Pro',
  meta: [
    {
      name: 'description',
      content: 'Urus dokumen kalendar akademik anda dengan mudah. Muat naik, pratonton, dan kemaskini kalendar akademik sekolah.'
    }
  ]
});

// =====================================================
// COMPOSABLES & STATE
// =====================================================

const { success: showSuccessToast, error: showErrorToast } = useToast();
const {
  loading,
  error,
  academicCalendarStatus,
  fetchAcademicCalendarDocument,
  uploadAcademicCalendarDocument,
  replaceAcademicCalendarDocument,
  deleteAcademicCalendarDocument,
  getTemporaryPublicUrl,
  clearError
} = useAcademicCalendarDocuments();

// Local state
const selectedFile = ref<File | null>(null);
const replaceFile = ref<File | null>(null);
const isUploading = ref(false);
const isReplacing = ref(false);
const isDeleting = ref(false);

// Modal states
const showReplaceModal = ref(false);
const showDeleteModal = ref(false);

// Document display states
const documentDisplayUrl = ref<string | null>(null);
const documentDisplayType = ref<'office' | 'image' | null>(null);
const newTabUrl = ref<string | null>(null);
const isDocumentLoading = ref(false);

// =====================================================
// COMPUTED PROPERTIES
// =====================================================

// =====================================================
// METHODS
// =====================================================

// File handling
const handleFileError = (errorMessage: string | null) => {
  if (errorMessage) {
    showErrorToast(errorMessage);
  }
  clearError();
};

// Upload new document
const handleUpload = async () => {
  if (!selectedFile.value) {
    showErrorToast('Sila pilih fail untuk dimuat naik');
    return;
  }

  isUploading.value = true;
  clearError();

  try {
    const result = await uploadAcademicCalendarDocument(selectedFile.value);

    if (result.success) {
      showSuccessToast('Kalendar akademik berjaya dimuat naik');
      selectedFile.value = null;
    } else {
      showErrorToast(result.error?.message || 'Gagal memuat naik kalendar akademik');
    }
  } catch (e) {
    console.error('Upload error:', e);
    showErrorToast('Ralat tidak dijangka semasa memuat naik');
  } finally {
    isUploading.value = false;
  }
};

// Replace existing document
const handleReplace = async () => {
  if (!replaceFile.value) {
    showErrorToast('Sila pilih fail untuk menggantikan');
    return;
  }

  isReplacing.value = true;
  clearError();

  try {
    const result = await replaceAcademicCalendarDocument(replaceFile.value);

    if (result.success) {
      showSuccessToast('Kalendar akademik berjaya diganti');
      closeReplaceModal();
    } else {
      showErrorToast(result.error?.message || 'Gagal menggantikan kalendar akademik');
    }
  } catch (e) {
    console.error('Replace error:', e);
    showErrorToast('Ralat tidak dijangka semasa menggantikan');
  } finally {
    isReplacing.value = false;
  }
};

// Delete document
const handleDelete = async () => {
  isDeleting.value = true;
  clearError();

  try {
    const success = await deleteAcademicCalendarDocument();

    if (success) {
      showSuccessToast('Kalendar akademik berjaya dipadam');
      showDeleteModal.value = false;
    } else {
      showErrorToast('Gagal memadam kalendar akademik');
    }
  } catch (e) {
    console.error('Delete error:', e);
    showErrorToast('Ralat tidak dijangka semasa memadam');
  } finally {
    isDeleting.value = false;
  }
};

// Load document for display
const loadDocumentDisplay = async () => {
  if (!academicCalendarStatus.value.document) {
    documentDisplayUrl.value = null;
    documentDisplayType.value = null;
    newTabUrl.value = null;
    return;
  }

  const document = academicCalendarStatus.value.document;
  documentDisplayType.value = getPreviewType(document.file_mime_type);
  documentDisplayUrl.value = null;
  newTabUrl.value = null;
  isDocumentLoading.value = true;

  try {
    const signedUrl = await getTemporaryPublicUrl(document.storage_file_path);

    if (signedUrl) {
      if (documentDisplayType.value === 'office') {
        if (document.file_mime_type === 'application/pdf') {
          // Use Google Docs viewer for PDF files in iframe
          documentDisplayUrl.value = `https://docs.google.com/gview?url=${encodeURIComponent(signedUrl)}&embedded=true`;
          // For new tab, open the PDF directly
          newTabUrl.value = signedUrl;
        } else {
          // Use Microsoft Office Online Viewer for other office documents (docx, xlsx)
          documentDisplayUrl.value = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(signedUrl)}`;
          // For new tab, use the non-embedded viewer which has more controls
          newTabUrl.value = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(signedUrl)}`;
        }
      } else if (documentDisplayType.value === 'image') {
        documentDisplayUrl.value = signedUrl;
        newTabUrl.value = signedUrl;
      }
    } else {
      showErrorToast('Gagal menjana pautan pratonton');
    }
  } catch (e) {
    console.error('Document display error:', e);
    showErrorToast('Ralat semasa memuat dokumen');
  } finally {
    isDocumentLoading.value = false;
  }
};

// Modal management
const closeReplaceModal = () => {
  showReplaceModal.value = false;
  replaceFile.value = null;
  clearError();
};

// Utility functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ms-MY', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// =====================================================
// WATCHERS
// =====================================================

// Watch for document changes and load display automatically
watch(
  () => academicCalendarStatus.value.document,
  async (newDocument) => {
    if (newDocument) {
      await loadDocumentDisplay();
    } else {
      documentDisplayUrl.value = null;
      documentDisplayType.value = null;
      newTabUrl.value = null;
    }
  },
  { immediate: false }
);

// =====================================================
// LIFECYCLE
// =====================================================

onMounted(async () => {
  await fetchAcademicCalendarDocument();
  // Load document display if document exists
  if (academicCalendarStatus.value.document) {
    await loadDocumentDisplay();
  }
});
</script>
