devtools.client.js?v=09f788cc:52 ✨ Nuxt DevTools  Press Shift + Alt + D to open DevTools 
runtime-core.esm-bundler.js?v=09f788cc:7060 <Suspense> is an experimental feature and its API will likely change.
jadual-mengajar.vue:450 Print Debug - Time Slots: 12
jadual-mengajar.vue:451 Print Debug - Timetable Entries: 1
jadual-mengajar.vue:452 Print Debug - Time Slots Data: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}}
jadual-mengajar.vue:453 Print Debug - Timetable Entries Data: Proxy(Array) {0: {…}}
jadual-mengajar.vue:487 Print Debug - Starting table generation...
jadual-mengajar.vue:490 Print Debug - Processing time slot 0: Proxy(Object) {id: '1', label: '7:20 AM - 7:50 AM', end_time: '07:50', start_time: '07:20', period_number: 0}
jadual-mengajar.vue:496 Print Debug - Time label: 7:20 AM - 7:50 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 1 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 1, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 1: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 1 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 1, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 1: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 1 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 1, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 1: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 1 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 1, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 1: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 1 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 1, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 1: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 1: Proxy(Object) {id: '2', label: '7:50 AM - 8:20 AM', end_time: '08:20', start_time: '07:50', period_number: 1}
jadual-mengajar.vue:496 Print Debug - Time label: 7:50 AM - 8:20 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 2 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 2, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 2: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 2 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 2, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 2: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 2 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 2, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 2: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 2 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 2, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 2: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 2 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 2, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 2: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 2: Proxy(Object) {id: '3', label: '8:20 AM - 8:50 AM', end_time: '08:50', start_time: '08:20', period_number: 2}
jadual-mengajar.vue:496 Print Debug - Time label: 8:20 AM - 8:50 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 3 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 3, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 3: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 3 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 3, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 3: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 3 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 3, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 3: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 3 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 3, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 3: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 3 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 3, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 3: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 3: Proxy(Object) {id: '4', label: '8:50 AM - 9:20 AM', end_time: '09:20', start_time: '08:50', period_number: 3}
jadual-mengajar.vue:496 Print Debug - Time label: 8:50 AM - 9:20 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 4 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 4, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 4: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 4 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 4, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 4: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 4 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 4, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 4: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 4 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 4, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 4: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 4 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 4, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 4: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 4: Proxy(Object) {id: '5', label: '9:20 AM - 9:50 AM', end_time: '09:50', start_time: '09:20', period_number: 4}
jadual-mengajar.vue:496 Print Debug - Time label: 9:20 AM - 9:50 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 5 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 5, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 5: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 5 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 5, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 5: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 5 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 5, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 5: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 5 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 5, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 5: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 5 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 5, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 5: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 5: Proxy(Object) {id: '6', label: '9:50 AM - 10:20 AM', end_time: '10:20', start_time: '09:50', period_number: 5}
jadual-mengajar.vue:496 Print Debug - Time label: 9:50 AM - 10:20 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 6 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 6, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 6: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 6 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 6, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 6: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 6 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 6, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 6: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 6 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 6, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 6: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 6 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 6, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 6: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 6: Proxy(Object) {id: '7', label: '10:20 AM - 10:50 AM', end_time: '10:50', start_time: '10:20', period_number: 6}
jadual-mengajar.vue:496 Print Debug - Time label: 10:20 AM - 10:50 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 7 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 7, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 7: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 7 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 7, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 7: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 7 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 7, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 7: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 7 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 7, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 7: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 7 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 7, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 7: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 7: Proxy(Object) {id: '8', label: '10:50 AM - 11:20 AM', end_time: '11:20', start_time: '10:50', period_number: 7}
jadual-mengajar.vue:496 Print Debug - Time label: 10:50 AM - 11:20 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 8 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 8, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 8: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 8 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 8, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 8: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 8 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 8, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 8: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 8 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 8, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 8: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 8 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 8, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 8: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 8: Proxy(Object) {id: '9', label: '11:20 AM - 11:50 AM', end_time: '11:50', start_time: '11:20', period_number: 8}
jadual-mengajar.vue:496 Print Debug - Time label: 11:20 AM - 11:50 AM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 9 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 9, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 9: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 9 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 9, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 9: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 9 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 9, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 9: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 9 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 9, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 9: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 9 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 9, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 9: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 9: Proxy(Object) {id: '10', label: '11:50 AM - 12:20 PM', end_time: '12:20', start_time: '11:50', period_number: 9}
jadual-mengajar.vue:496 Print Debug - Time label: 11:50 AM - 12:20 PM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 10 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 10, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 10: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 10 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 10, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 10: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 10 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 10, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 10: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 10 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 10, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 10: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 10 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 10, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 10: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 10: Proxy(Object) {id: '11', label: '12:20 PM - 12:50 PM', end_time: '12:50', start_time: '12:20', period_number: 10}
jadual-mengajar.vue:496 Print Debug - Time label: 12:20 PM - 12:50 PM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 11 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 11, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 11: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 11 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 11, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 11: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 11 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 11, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 11: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 11 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 11, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 11: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 11 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 11, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 11: undefined
jadual-mengajar.vue:490 Print Debug - Processing time slot 11: Proxy(Object) {id: '12', label: '12:50 PM - 1:20 PM', end_time: '13:20', start_time: '12:50', period_number: 11}
jadual-mengajar.vue:496 Print Debug - Time label: 12:50 PM - 1:20 PM
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 12 && ISNIN === ISNIN = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 12, ISNIN: undefined
jadual-mengajar.vue:507 Print Debug - Checking ISNIN for slot 12: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 12 && ISNIN === SELASA = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 12, SELASA: undefined
jadual-mengajar.vue:507 Print Debug - Checking SELASA for slot 12: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 12 && ISNIN === RABU = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 12, RABU: undefined
jadual-mengajar.vue:507 Print Debug - Checking RABU for slot 12: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 12 && ISNIN === KHAMIS = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 12, KHAMIS: undefined
jadual-mengajar.vue:507 Print Debug - Checking KHAMIS for slot 12: undefined
jadual-mengajar.vue:469 Print Debug - Comparing: 07:20:00-07:50:00 === 12 && ISNIN === JUMAAT = false
jadual-mengajar.vue:472 Print Debug - getTimetableEntry result for 12, JUMAAT: undefined
jadual-mengajar.vue:507 Print Debug - Checking JUMAAT for slot 12: undefined