<template>
    <UiCompositeModal :is-open="isOpen" :title="modalTitle" size="lg" @update:is-open="handleClose">
        <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Event Title -->
            <div>
                <UiBaseInput v-model="formData.title" label="Tajuk Acara" placeholder="Masukkan tajuk acara" required
                    :error="errors.title" />
            </div>

            <!-- Event Category -->
            <div>
                <UiBaseSingleSelect v-model="formData.category" label="Kategori" placeholder="Pilih kategori acara"
                    :options="categoryOptions" required :error="errors.category" />
            </div>

            <!-- Event Description -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Penerangan
                </label>
                <textarea v-model="formData.description" rows="3" class="form-input resize-none"
                    placeholder="Penerangan tambahan tentang acara (pilihan)"></textarea>
            </div>

            <!-- Date and Time -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Start Date -->
                <div>
                    <UiBaseDatePicker v-model="formData.start_date" label="Tarikh Mula" placeholder="Pilih tarikh mula"
                        required :error="errors.start_date" />
                </div>

                <!-- End Date -->
                <div>
                    <UiBaseDatePicker v-model="formData.end_date" label="Tarikh Akhir"
                        placeholder="Pilih tarikh akhir (pilihan)" :min="formData.start_date" />
                </div>
            </div>



            <!-- Location -->
            <div>
                <UiBaseInput v-model="formData.location" label="Lokasi" placeholder="Lokasi acara (pilihan)"
                    prepend-icon="heroicons:map-pin-solid" />
            </div>


        </form>

        <template #footer>
            <div class="flex items-center justify-end space-x-3">
                <UiBaseButton v-if="isEditing" variant="delete" size="md" prepend-icon="heroicons:trash-solid"
                    @click="handleDelete" :disabled="loading">
                    Padam
                </UiBaseButton>
                <UiBaseButton variant="outline" @click="handleClose" :disabled="loading">
                    Batal
                </UiBaseButton>
                <UiBaseButton variant="primary" @click="handleSubmit" :disabled="loading || !isFormValid"
                    prepend-icon="heroicons:check-solid">
                    <UiBaseIcon v-if="loading" name="mdi:loading" class="mr-2 h-4 w-4 animate-spin" />
                    {{ isEditing ? 'Kemaskini' : 'Simpan' }}
                </UiBaseButton>
            </div>
        </template>
    </UiCompositeModal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { CalendarEvent, CalendarEventFormData, EventCategory } from '~/types/calendar'
import { EVENT_CATEGORIES } from '~/types/calendar'

// Props
interface Props {
    isOpen: boolean
    event?: CalendarEvent | null
    selectedDate?: string | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
    'close': []
    'saved': [event: CalendarEvent]
    'deleted': [eventId: string]
}>()

// Reactive state
const loading = ref(false)
const errors = ref<Record<string, string>>({})

// Form data
const formData = ref<CalendarEventFormData>({
    title: '',
    description: '',
    category: 'OTHER' as EventCategory,
    start_date: '',
    end_date: '',
    location: ''
})

// Computed
const isEditing = computed(() => !!props.event)
const modalTitle = computed(() => isEditing.value ? 'Kemaskini Acara' : 'Tambah Acara Baru')

const categoryOptions = computed(() =>
    EVENT_CATEGORIES.map(cat => ({
        value: cat.value,
        label: cat.label,
        icon: cat.icon
    }))
)

const recurrenceOptions = [
    { value: 'daily', label: 'Harian' },
    { value: 'weekly', label: 'Mingguan' },
    { value: 'monthly', label: 'Bulanan' },
    { value: 'yearly', label: 'Tahunan' }
]

const isFormValid = computed(() => {
    return formData.value.title.trim() !== '' &&
        formData.value.category.length > 0 &&
        formData.value.start_date !== ''
})

// Methods
const resetForm = () => {
    formData.value = {
        title: '',
        description: '',
        category: 'OTHER' as EventCategory,
        start_date: props.selectedDate || '',
        end_date: '',
        start_time: '',
        end_time: '',
        location: '',
        is_all_day: false,
        is_recurring: false
    }

    recurrenceType.value = 'weekly'
    recurrenceInterval.value = 1
    recurrenceEndDate.value = ''
    errors.value = {}
}

const loadEventData = () => {
    if (props.event) {
        formData.value = {
            title: props.event.title,
            description: props.event.description || '',
            category: props.event.category,
            start_date: props.event.start_date,
            end_date: props.event.end_date || '',
            location: props.event.location || ''
        }
    }
}

const validateForm = () => {
    errors.value = {}

    if (!formData.value.title.trim()) {
        errors.value.title = 'Tajuk acara diperlukan'
    }

    if (!formData.value.category) {
        errors.value.category = 'Kategori diperlukan'
    }

    if (!formData.value.start_date) {
        errors.value.start_date = 'Tarikh mula diperlukan'
    }

    return Object.keys(errors.value).length === 0
}

const handleSubmit = async () => {
    if (!validateForm()) return

    loading.value = true

    try {
        const eventData: CalendarEvent = {
            id: props.event?.id || '',
            user_id: '', // Will be set by the API
            title: formData.value.title,
            description: formData.value.description || undefined,
            category: formData.value.category,
            start_date: formData.value.start_date,
            end_date: formData.value.end_date || undefined,
            location: formData.value.location || undefined,
            created_at: props.event?.created_at || new Date().toISOString(),
            updated_at: new Date().toISOString()
        }

        emit('saved', eventData)
    } catch (error) {
        console.error('Error saving event:', error)
    } finally {
        loading.value = false
    }
}

const handleDelete = async () => {
    if (!props.event) return

    loading.value = true

    try {
        emit('deleted', props.event.id)
    } catch (error) {
        console.error('Error deleting event:', error)
    } finally {
        loading.value = false
    }
}

const handleClose = () => {
    emit('close')
}



// Watchers
watch(() => props.isOpen, (isOpen) => {
    if (isOpen) {
        if (props.event) {
            loadEventData()
        } else {
            resetForm()
        }
    }
})



// Auto-set end date when start date changes (if end date is empty)
watch(() => formData.value.start_date, (newStartDate) => {
    if (newStartDate && !formData.value.end_date) {
        // Set end date to the same date as start date by default
        formData.value.end_date = newStartDate
    }
})
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
