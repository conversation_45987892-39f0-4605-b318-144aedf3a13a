// Calendar and Annual School Events Types

export type EventCategory = 
  | 'ACADEMIC'      // Academic activities (exams, assessments)
  | 'HOLIDAY'       // Public holidays and school breaks
  | 'COCURRICULAR'  // Co-curricular activities and sports
  | 'MEETING'       // Staff meetings and parent meetings
  | 'CEREMONY'      // School ceremonies and assemblies
  | 'TRAINING'      // Professional development and training
  | 'OTHER'         // Other miscellaneous events

export interface EventCategoryConfig {
  value: EventCategory
  label: string
  icon: string
  color: string
  bgColor: string
  textColor: string
  description: string
}

export interface CalendarEvent {
  id: string
  user_id: string
  title: string
  description?: string
  category: EventCategory
  start_date: string // ISO date string (YYYY-MM-DD)
  end_date?: string  // ISO date string for multi-day events
  location?: string
  color?: string // Custom color override
  created_at: string
  updated_at: string
}

export interface CalendarEventFormData {
  title: string
  description?: string
  category: EventCategory
  start_date: string
  end_date?: string
  location?: string
}

export interface CalendarDay {
  date: Date
  dateString: string // YYYY-MM-DD
  isCurrentMonth: boolean
  isToday: boolean
  events: CalendarEvent[]
}

export interface CalendarMonth {
  year: number
  month: number // 0-11 (JavaScript month format)
  monthName: string
  days: CalendarDay[]
}

export interface CalendarFilters {
  categories: EventCategory[]
  searchQuery: string
}

// Event category configurations for UI
export const EVENT_CATEGORIES: EventCategoryConfig[] = [
  {
    value: 'ACADEMIC',
    label: 'Akademik',
    icon: 'heroicons:academic-cap-solid',
    color: 'blue-600',
    bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    textColor: 'text-blue-800 dark:text-blue-200',
    description: 'Peperiksaan, penilaian, dan aktiviti akademik'
  },
  {
    value: 'HOLIDAY',
    label: 'Cuti & Perayaan',
    icon: 'heroicons:calendar-days-solid',
    color: 'green-600',
    bgColor: 'bg-green-100 dark:bg-green-900/30',
    textColor: 'text-green-800 dark:text-green-200',
    description: 'Cuti umum, cuti sekolah, dan perayaan'
  },
  {
    value: 'COCURRICULAR',
    label: 'Kokurikulum',
    icon: 'heroicons:trophy-solid',
    color: 'orange-600',
    bgColor: 'bg-orange-100 dark:bg-orange-900/30',
    textColor: 'text-orange-800 dark:text-orange-200',
    description: 'Sukan, pertandingan, dan aktiviti kokurikulum'
  },
  {
    value: 'MEETING',
    label: 'Mesyuarat',
    icon: 'heroicons:users-solid',
    color: 'purple-600',
    bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    textColor: 'text-purple-800 dark:text-purple-200',
    description: 'Mesyuarat guru, ibu bapa, dan pentadbiran'
  },
  {
    value: 'CEREMONY',
    label: 'Majlis & Upacara',
    icon: 'heroicons:megaphone-solid',
    color: 'red-600',
    bgColor: 'bg-red-100 dark:bg-red-900/30',
    textColor: 'text-red-800 dark:text-red-200',
    description: 'Perhimpunan, majlis, dan upacara sekolah'
  },
  {
    value: 'TRAINING',
    label: 'Latihan & Kursus',
    icon: 'heroicons:book-open-solid',
    color: 'indigo-600',
    bgColor: 'bg-indigo-100 dark:bg-indigo-900/30',
    textColor: 'text-indigo-800 dark:text-indigo-200',
    description: 'Pembangunan profesional dan latihan'
  },
  {
    value: 'OTHER',
    label: 'Lain-lain',
    icon: 'heroicons:ellipsis-horizontal-circle-solid',
    color: 'gray-600',
    bgColor: 'bg-gray-100 dark:bg-gray-900/30',
    textColor: 'text-gray-800 dark:text-gray-200',
    description: 'Aktiviti dan acara lain-lain'
  }
]

// Helper functions
export function getCategoryConfig(category: EventCategory): EventCategoryConfig {
  return EVENT_CATEGORIES.find(config => config.value === category) || EVENT_CATEGORIES[EVENT_CATEGORIES.length - 1]
}

export function formatEventDate(event: CalendarEvent): string {
  const startDate = new Date(event.start_date)
  const endDate = event.end_date ? new Date(event.end_date) : null

  if (endDate && endDate.getTime() !== startDate.getTime()) {
    return `${startDate.toLocaleDateString('ms-MY')} - ${endDate.toLocaleDateString('ms-MY')}`
  }
  return startDate.toLocaleDateString('ms-MY')
}

export function isEventOnDate(event: CalendarEvent, date: string): boolean {
  const eventStart = event.start_date
  const eventEnd = event.end_date || event.start_date
  
  return date >= eventStart && date <= eventEnd
}

// Malaysian school calendar helpers
export const MALAYSIAN_MONTHS = [
  'Januari', 'Februari', 'Mac', 'April', 'Mei', 'Jun',
  'Julai', 'Ogos', 'September', 'Oktober', 'November', 'Disember'
]

export const MALAYSIAN_DAYS = [
  'Ahad', 'Isnin', 'Selasa', 'Rabu', 'Khamis', 'Jumaat', 'Sabtu'
]

export function getMalaysianMonthName(monthIndex: number): string {
  return MALAYSIAN_MONTHS[monthIndex] || ''
}

export function getMalaysianDayName(dayIndex: number): string {
  return MALAYSIAN_DAYS[dayIndex] || ''
}
