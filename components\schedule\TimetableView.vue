<template>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <!-- Header Component -->
        <TimetableHeader :edit-mode="editMode" :timetable-entries="timetableEntries" @toggle-edit="editMode = !editMode"
            @open-clear-all="openClearAllModal" @open-class-subject="openClassSubjectModal" />

        <!-- Loading State -->
        <div v-if="timetableLoading || timeSlotsLoading" class="p-6">
            <SkeletonLoader variant="table" :rows="8" :columns="7" show-header spacing="sm" />
        </div>

        <!-- Grid Component -->
        <TimetableGrid v-else :days="days" :time-slots="timeSlots" :timetable-entries="timetableEntries"
            :edit-mode="editMode" :get-timetable-entry="getTimetableEntry" :get-slot-classes="getSlotClasses"
            :get-entry-color="getEntryColor" @edit-entry="editEntry" @delete-entry="confirmDeleteEntry"
            @add-time-slot="handleAddTimeSlot" @edit-time-slot="editTimeSlot" @open-add-modal="openAddModal"
            @switch-teaching-days="handleSwitchTeachingDays" />

        <!-- Legend Component -->
        <TimetableLegend :used-subjects="usedSubjects" :used-activities="usedActivities" />
    </div>

    <!-- Add/Edit Modal -->
    <ActivityEntryModal :is-open="isModalOpen" :entry="editingEntry" :time-slot="selectedTimeSlot" :day="selectedDay"
        :user-class-subjects="userClassSubjects" @close="closeModal" @saved="handleEntrySaved" />

    <!-- Delete Confirmation Modal -->
    <UiCompositeDeleteConfirmationModal :is-open="isDeleteModalOpen" item-type="kelas"
        :item-name="entryToDelete ? `${entryToDelete.class_name} - ${entryToDelete.subject_name}` : ''"
        :item-subtitle="entryToDelete ? `${getDayLabel(entryToDelete.day)}, ${getTimeSlotLabel(entryToDelete)}` : ''"
        warning-message="Kelas ini akan dipadam daripada jadual waktu anda. Tindakan ini tidak boleh dibatalkan."
        :loading="timetableLoading" @confirm="handleDeleteEntry" @cancel="isDeleteModalOpen = false" />

    <!-- Time Slot Delete Confirmation Modal -->
    <UiCompositeDeleteConfirmationModal :is-open="isTimeSlotDeleteModalOpen" item-type="slot waktu"
        :item-name="editingTimeSlot ? `Waktu ${editingTimeSlot.period_number + 1}` : ''"
        :item-subtitle="editingTimeSlot ? `${formatTime(editingTimeSlot.start_time)} - ${formatTime(editingTimeSlot.end_time)}` : ''"
        warning-message="Slot waktu ini akan dipadam secara kekal daripada jadual anda. Tindakan ini tidak boleh dibatalkan."
        :loading="isDeletingTimeSlot" loading-text="Memadam slot waktu..." :z-index="70"
        @confirm="confirmDeleteTimeSlot" @cancel="isTimeSlotDeleteModalOpen = false" />

    <!-- Time Slot Edit Modal -->
    <TimeSlotEditModal :is-open="isTimeEditModalOpen" :time-slot="editingTimeSlot"
        :affected-classes="getAffectedClasses()" @update:is-open="isTimeEditModalOpen = $event"
        @update="handleTimeSlotUpdate" @delete="handleDeleteTimeSlot" />

    <!-- Class Subject Management Modal -->
    <ClassSubjectModal :is-open="isClassSubjectModalOpen" :user-class-subjects="userClassSubjects"
        @update:is-open="isClassSubjectModalOpen = $event" @save="saveClassSubjects" @close="closeClassSubjectModal" />



    <!-- Clear All Confirmation Modal -->
    <UiCompositeDeleteConfirmationModal :is-open="isClearAllModalOpen" title="Kosongkan Semua Jadual"
        item-type="slot jadual waktu" :item-name="`${timetableEntries.length} slot`" danger-level="high"
        impact-severity="high" :loading="isClearingAll" :z-index="60" @confirm="handleClearAll"
        @cancel="isClearAllModalOpen = false">
        <template #description>
            <p class="text-gray-600 dark:text-gray-400">
                Tindakan ini akan mengosongkan <strong>semua slot jadual waktu</strong> yang telah ditetapkan.
            </p>
        </template>
        <template #impact>
            <div class="space-y-3">
                <p class="font-medium text-gray-900 dark:text-white">
                    Semua <strong>{{ timetableEntries.length }} slot jadual waktu</strong> akan dipadam:
                </p>
                <div class="max-h-40 overflow-y-auto space-y-2">
                    <div v-for="day in days" :key="day.value">
                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            {{ day.label }}
                        </div>
                        <div class="space-y-1 ml-3">
                            <div v-for="entry in getEntriesForDay(day.value)" :key="entry.id"
                                class="text-xs flex items-center space-x-2">
                                <Icon name="mdi:calendar-remove" class="h-3 w-3 text-red-500" />
                                <span>{{ getTimeSlotLabel(entry) }} - {{ entry.class_name }}</span>
                            </div>
                        </div>
                        <div v-if="getEntriesForDay(day.value).length === 0" class="text-xs text-gray-500 ml-3">
                            Tiada slot
                        </div>
                    </div>
                </div>
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                    <p class="text-xs font-medium text-red-800 dark:text-red-200">
                        ⚠️ Amaran: Semua data jadual waktu akan dipadam secara kekal dan tidak boleh dipulihkan.
                    </p>
                </div>
            </div>
        </template>
    </UiCompositeDeleteConfirmationModal>

    <!-- Class Subject Delete Confirmation Modal -->
    <UiCompositeDeleteConfirmationModal :is-open="isClassSubjectDeleteConfirmModalOpen" title="Padam Kelas & Subjek"
        item-type="kombinasi kelas-subjek" :item-name="`${classSubjectsToDelete.length} kombinasi kelas-subjek`"
        danger-level="high" impact-severity="high" :z-index="60" @confirm="proceedWithClassSubjectUpdate"
        @cancel="clearClassSubjectDeleteConfirmation">
        <template #item-details>
            <div class="space-y-1">
                <div v-for="cs in classSubjectsToDelete" :key="`${cs.class_id}-${cs.subject_id}`"
                    class="flex items-center space-x-2 text-sm">
                    <Icon name="mdi:minus-circle" class="h-4 w-4 text-red-500" />
                    <span>{{ cs.className }} - {{ getSubjectNameById(cs.subject_id) }}</span>
                </div>
            </div>
        </template>
        <template #impact>
            <div v-if="timetableEntriesToDelete.length > 0">
                <p class="font-medium mb-2">
                    <strong>{{ timetableEntriesToDelete.length }} slot jadual waktu</strong> akan dipadam secara
                    automatik:
                </p>
                <div class="max-h-32 overflow-y-auto space-y-1">
                    <div v-for="entry in timetableEntriesToDelete" :key="entry.id"
                        class="text-xs flex items-center space-x-2">
                        <Icon name="mdi:calendar-remove" class="h-3 w-3" />
                        <span>{{ getDayLabel(entry.day) }}, {{ getTimeSlotLabel(entry) }} - {{ entry.class_name
                            }}</span>
                    </div>
                </div>
                <p class="text-xs mt-2 font-medium">
                    Amaran: Semua data yang berkaitan akan dipadam secara kekal dan tidak boleh dipulihkan.
                </p>
            </div>
        </template>
    </UiCompositeDeleteConfirmationModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import TimetableHeader from './TimetableHeader.vue'
import TimetableGrid from './TimetableGrid.vue'
import TimetableLegend from './TimetableLegend.vue'
import TimeSlotEditModal from './TimeSlotEditModal.vue'
import ClassSubjectModal from './ClassSubjectModal.vue'
import Button from '~/components/ui/base/Button.vue'
import Icon from '~/components/ui/base/Icon.vue'
import UiCompositeDeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue'
import ActivityEntryModal from '~/components/schedule/ActivityEntryModal.vue'
import SkeletonLoader from '~/components/ui/skeleton/SkeletonLoader.vue'
import { useToast } from '~/composables/useToast'
import type {
    TimetableEntry,
    TimeSlot,
    DayOfWeek
} from '~/types/timetable'
import { isClassActivity } from '~/types/timetable'
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'
import { useSubjects } from '~/composables/useSubjects'
import { useTimetable } from '~/composables/useTimetable'
import { useTimeSlots } from '~/composables/useTimeSlots'
import { useSupabase } from '~/composables/useSupabase'
import { useSupabaseUser, useSupabaseClient } from '#imports'
import useSubjectColors from '~/composables/useSubjectColors'
import {
    formatTime,
    formatTimeRange,
    normalizeTimeFormat,
    calculateDurationMinutes,
    addDurationToTime
} from '~/utils/timeHelpers'
import { ACTIVITY_TYPES } from '~/types/timetable'

const { getSubjectColor, getEntryColor } = useSubjectColors()

const editMode = ref(false)

// Teaching days mode state
const teachingDaysMode = ref<'weekdays' | 'sunday-thursday'>('weekdays')

interface Props {
    userClassSubjects?: UserClassSubjectEntry[]
}

interface Emits {
    (e: 'update-class-subjects'): void
}

const props = withDefaults(defineProps<Props>(), {
    userClassSubjects: () => []
})

const emit = defineEmits<Emits>()

// Supabase
const supabase = useSupabaseClient()
const user = useSupabaseUser()

const { subjects, fetchSubjects } = useSubjects()
const {
    timetableEntries,
    loading: timetableLoading,
    fetchTimetableEntries,
    createTimetableEntry,
    updateTimetableEntry,
    deleteTimetableEntry
} = useTimetable()

const {
    timeSlots,
    loading: timeSlotsLoading,
    fetchTimeSlots,
    updateTimeSlot,
    addTimeSlot,
    deleteTimeSlot
} = useTimeSlots()

// Toast notifications
const { success: showSuccessToast, error: showErrorToast } = useToast()

const days = computed(() => {
    if (teachingDaysMode.value === 'sunday-thursday') {
        return [
            { value: 'AHAD' as DayOfWeek, label: 'Ahad' },
            { value: 'ISNIN' as DayOfWeek, label: 'Isnin' },
            { value: 'SELASA' as DayOfWeek, label: 'Selasa' },
            { value: 'RABU' as DayOfWeek, label: 'Rabu' },
            { value: 'KHAMIS' as DayOfWeek, label: 'Khamis' }
        ]
    } else {
        return [
            { value: 'ISNIN' as DayOfWeek, label: 'Isnin' },
            { value: 'SELASA' as DayOfWeek, label: 'Selasa' },
            { value: 'RABU' as DayOfWeek, label: 'Rabu' },
            { value: 'KHAMIS' as DayOfWeek, label: 'Khamis' },
            { value: 'JUMAAT' as DayOfWeek, label: 'Jumaat' }
        ]
    }
})

const isModalOpen = ref(false)
const isDeleteModalOpen = ref(false)
const isTimeEditModalOpen = ref(false)
const isTimeSlotDeleteModalOpen = ref(false)
const isClassSubjectModalOpen = ref(false)
const isClassSubjectDeleteConfirmModalOpen = ref(false)
const isClearAllModalOpen = ref(false)
const isTimeSlotUpdating = ref(false)
const isClearingAll = ref(false)
const isDeletingTimeSlot = ref(false)
const editingEntry = ref<TimetableEntry | null>(null)
const entryToDelete = ref<TimetableEntry | null>(null)
const editingTimeSlot = ref<TimeSlot | null>(null)
const selectedTimeSlot = ref<TimeSlot | null>(null)
const selectedDay = ref<DayOfWeek | null>(null)

const classSubjectsToDelete = ref<UserClassSubjectEntry[]>([])
const timetableEntriesToDelete = ref<TimetableEntry[]>([])
const pendingClassSubjectsUpdate = ref<UserClassSubjectEntry[]>([])

const usedSubjects = computed(() => {
    const subjectIds = new Set(
        timetableEntries.value
            .filter(entry => isClassActivity(entry))
            .map(entry => entry.subject_id)
            .filter((id): id is string => id !== null && id !== undefined)
    )
    return Array.from(subjectIds).map(id => {
        const subject = subjects.value.find(s => s.id === id)
        const subjectColor = getSubjectColor(id, subject?.name || null)
        return {
            ...subjectColor,
            subject_name: subject?.name || subjectColor.subject_name
        }
    })
})

const usedActivities = computed(() => {
    const activityTypes = new Set(
        timetableEntries.value
            .filter(entry => !isClassActivity(entry))
            .map(entry => entry.activity_type)
    )
    return Array.from(activityTypes).map(activityType => ACTIVITY_TYPES[activityType])
})


const ensureAuthenticated = (): { user: any, client: any } | null => {
    const user = useSupabaseUser()
    const { client } = useSupabase()
    if (!user.value) return null
    return { user: user.value, client }
}

const logError = (context: string, error: unknown): void => {
    console.error(`Error in ${context}:`, error)
}

const findSubjectById = (subjectId: string | null): { id: string, name: string } | null => {
    if (!subjectId) return null
    return subjects.value.find(s => s.id === subjectId) || null
}

const getTimetableEntry = (timeSlotId: string, day: DayOfWeek): TimetableEntry | undefined => {
    const timeSlot = timeSlots.value.find(ts => ts.id === timeSlotId)
    if (!timeSlot) return undefined
    const normalizedStartTime = normalizeTimeFormat(timeSlot.start_time)
    const normalizedEndTime = normalizeTimeFormat(timeSlot.end_time)
    return timetableEntries.value.find(e =>
        e.day === day &&
        normalizeTimeFormat(e.time_slot_start) === normalizedStartTime &&
        normalizeTimeFormat(e.time_slot_end) === normalizedEndTime
    )
}

const getSlotClasses = (timeSlot: TimeSlot, day: DayOfWeek): string => {
    if (timeSlot.period_number < 0) {
        return 'bg-yellow-50 dark:bg-yellow-900/10'
    }
    const hasEntry = getTimetableEntry(timeSlot.id, day)
    return hasEntry ? '' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
}

const getDayLabel = (day: DayOfWeek): string => {
    const dayOption = days.value.find(d => d.value === day)
    return dayOption ? dayOption.label : day
}

const getSubjectNameById = (subjectId: string | null): string => {
    const subject = findSubjectById(subjectId)
    return subject?.name || 'Subjek'
}

const getTimeSlotLabel = (entry: TimetableEntry): string => {
    return formatTimeRange(entry.time_slot_start, entry.time_slot_end)
}


const getAffectedClasses = (): TimetableEntry[] => {
    if (!editingTimeSlot.value) return []
    const originalTimeSlot = timeSlots.value.find(ts => ts.id === editingTimeSlot.value!.id)
    if (!originalTimeSlot) return []
    return timetableEntries.value.filter(entry => {
        const normalizedEntryStart = normalizeTimeFormat(entry.time_slot_start)
        const normalizedEntryEnd = normalizeTimeFormat(entry.time_slot_end)
        const normalizedOriginalStart = normalizeTimeFormat(originalTimeSlot.start_time)
        const normalizedOriginalEnd = normalizeTimeFormat(originalTimeSlot.end_time)
        return normalizedEntryStart === normalizedOriginalStart && normalizedEntryEnd === normalizedOriginalEnd
    })
}

const handleTimeSlotUpdate = async (updatedTimeSlot: TimeSlot) => {
    console.log('handleTimeSlotUpdate called with:', updatedTimeSlot)
    if (!updatedTimeSlot) return

    // Update the editingTimeSlot with the new values from the modal
    editingTimeSlot.value = { ...updatedTimeSlot }
    const originalTimeSlot = timeSlots.value.find(ts => ts.id === editingTimeSlot.value!.id)
    if (!originalTimeSlot) return
    const affectedClasses = getAffectedClasses()
    isTimeSlotUpdating.value = true
    try {
        const timeSlotIndex = timeSlots.value.findIndex(ts => ts.id === editingTimeSlot.value!.id)
        if (timeSlotIndex !== -1) {
            timeSlots.value[timeSlotIndex] = { ...editingTimeSlot.value }
        }
        affectedClasses.forEach(entry => {
            const entryIndex = timetableEntries.value.findIndex(e => e.id === entry.id)
            if (entryIndex !== -1) {
                timetableEntries.value[entryIndex] = {
                    ...timetableEntries.value[entryIndex],
                    time_slot_start: editingTimeSlot.value!.start_time,
                    time_slot_end: editingTimeSlot.value!.end_time
                }
            }
        })
        const success = await updateTimeSlot(editingTimeSlot.value.id, {
            start_time: editingTimeSlot.value.start_time,
            end_time: editingTimeSlot.value.end_time,
            label: editingTimeSlot.value.period_number >= 0
                ? formatTimeRange(editingTimeSlot.value.start_time, editingTimeSlot.value.end_time)
                : editingTimeSlot.value.label
        })
        if (!success) throw new Error('Failed to update time slot')
        const updatePromises = affectedClasses.map(entry =>
            updateTimetableEntry(entry.id, {
                time_slot_start: editingTimeSlot.value!.start_time,
                time_slot_end: editingTimeSlot.value!.end_time
            })
        )
        await Promise.all(updatePromises)

        // Show success message
        showSuccessToast('Waktu slot telah berjaya dikemas kini.')

        isTimeEditModalOpen.value = false
        editingTimeSlot.value = null
    } catch (error) {
        logError('updating time slot', error)
        const timeSlotIndex = timeSlots.value.findIndex(ts => ts.id === originalTimeSlot.id)
        if (timeSlotIndex !== -1) timeSlots.value[timeSlotIndex] = originalTimeSlot
        affectedClasses.forEach(entry => {
            const entryIndex = timetableEntries.value.findIndex(e => e.id === entry.id)
            if (entryIndex !== -1) timetableEntries.value[entryIndex] = entry
        })
        showErrorToast('Ralat: Gagal mengemas kini waktu. Sila cuba lagi.')
    } finally {
        isTimeSlotUpdating.value = false
    }
}

const handleAddTimeSlot = async () => {
    try {
        const newSlot = await addTimeSlot()
        if (!newSlot) {
            showErrorToast('Gagal menambah slot waktu baharu')
        }
    } catch (err) {
        showErrorToast('Ralat berlaku semasa menambah slot waktu')
    }
}

const handleDeleteTimeSlot = () => {
    if (!editingTimeSlot.value) return
    const affectedClasses = getAffectedClasses()
    if (affectedClasses.length > 0) {
        showErrorToast(`Tidak boleh padam slot waktu ini kerana terdapat ${affectedClasses.length} kelas yang menggunakannya.`)
        return
    }
    isTimeSlotDeleteModalOpen.value = true
}

const confirmDeleteTimeSlot = async () => {
    if (!editingTimeSlot.value) return
    isDeletingTimeSlot.value = true
    try {
        const success = await deleteTimeSlot(editingTimeSlot.value.id)
        if (success) {
            isTimeSlotDeleteModalOpen.value = false
            isTimeEditModalOpen.value = false
            editingTimeSlot.value = null
        } else {
            showErrorToast('Gagal memadam slot waktu. Sila cuba lagi.')
        }
    } catch (err) {
        showErrorToast('Ralat berlaku semasa memadam slot waktu')
    } finally {
        isDeletingTimeSlot.value = false
    }
}

const openAddModal = (timeSlotId?: string, day?: DayOfWeek) => {
    editingEntry.value = null
    if (timeSlotId && day) {
        selectedTimeSlot.value = timeSlots.value.find(ts => ts.id === timeSlotId) || null
        selectedDay.value = day
    } else {
        selectedTimeSlot.value = null
        selectedDay.value = null
    }
    isModalOpen.value = true
}

const editTimeSlot = (timeSlot: TimeSlot) => {
    editingTimeSlot.value = { ...timeSlot }
    isTimeEditModalOpen.value = true
}

const editEntry = (entry: TimetableEntry) => {
    editingEntry.value = entry
    selectedTimeSlot.value = {
        id: `${entry.time_slot_start}-${entry.time_slot_end}`,
        start_time: entry.time_slot_start,
        end_time: entry.time_slot_end,
        label: getTimeSlotLabel(entry),
        period_number: -1
    }
    selectedDay.value = entry.day
    isModalOpen.value = true
}

const closeModal = () => {
    isModalOpen.value = false
    editingEntry.value = null
    selectedTimeSlot.value = null
    selectedDay.value = null
}

const openClassSubjectModal = () => {
    isClassSubjectModalOpen.value = true
}

const closeClassSubjectModal = () => {
    isClassSubjectModalOpen.value = false
}

const openClearAllModal = () => {
    isClearAllModalOpen.value = true
}

// Teaching days mode functions
const handleSwitchTeachingDays = async () => {
    const newMode = teachingDaysMode.value === 'weekdays' ? 'sunday-thursday' : 'weekdays'

    try {
        // Migrate existing timetable entries
        await migrateTimetableEntries(teachingDaysMode.value, newMode)

        // Update the mode
        teachingDaysMode.value = newMode

        // Save to user profile
        await saveTeachingDaysModeToProfile(newMode)

        // Refresh timetable data
        await fetchTimetableEntries()

        showSuccessToast('Mod hari mengajar telah ditukar dan data telah dipindahkan.')
    } catch (error) {
        console.error('Error switching teaching days mode:', error)
        showErrorToast('Gagal menukar mod hari mengajar. Sila cuba lagi.')
    }
}

// Migration function for timetable entries
const migrateTimetableEntries = async (fromMode: string, toMode: string) => {
    const entriesToUpdate: TimetableEntry[] = []

    // Define migration mapping
    const weekdaysToSundayThursday: Partial<Record<DayOfWeek, DayOfWeek>> = {
        'ISNIN': 'AHAD',
        'SELASA': 'ISNIN',
        'RABU': 'SELASA',
        'KHAMIS': 'RABU',
        'JUMAAT': 'KHAMIS'
    }

    const sundayThursdayToWeekdays: Partial<Record<DayOfWeek, DayOfWeek>> = {
        'AHAD': 'ISNIN',
        'ISNIN': 'SELASA',
        'SELASA': 'RABU',
        'RABU': 'KHAMIS',
        'KHAMIS': 'JUMAAT'
    }

    const migrationMap = fromMode === 'weekdays' ? weekdaysToSundayThursday : sundayThursdayToWeekdays

    // Find entries that need migration
    for (const entry of timetableEntries.value) {
        const newDay = migrationMap[entry.day]
        if (newDay && newDay !== entry.day) {
            entriesToUpdate.push({
                ...entry,
                day: newDay
            })
        }
    }

    // Update entries in database
    for (const entry of entriesToUpdate) {
        await updateTimetableEntry(entry.id, entry)
    }
}

// Save teaching days mode to user profile
const saveTeachingDaysModeToProfile = async (mode: string) => {
    if (!user.value) return

    try {
        // Use direct update with type assertion
        const { error } = await (supabase as any)
            .from('profiles')
            .update({ teaching_days_mode: mode })
            .eq('id', user.value.id)

        if (error) {
            // If column doesn't exist, warn but don't throw
            if (error.code === '42703') {
                console.warn('teaching_days_mode column not found in profiles table. Please add it to enable persistence.')
                return
            }
            console.error('Error saving teaching days mode:', error)
            throw error
        }
    } catch (err) {
        console.error('Error saving teaching days mode:', err)
        throw err
    }
}

// Load teaching days mode from user profile
const loadTeachingDaysModeFromProfile = async () => {
    if (!user.value) return

    try {
        const { data, error } = await supabase
            .from('profiles')
            .select('teaching_days_mode')
            .eq('id', user.value.id)
            .single()

        if (error) {
            // If column doesn't exist, silently use default
            if (error.code === '42703') {
                console.log('teaching_days_mode column not found, using default weekdays mode')
                teachingDaysMode.value = 'weekdays'
                return
            }
            console.error('Error loading teaching days mode:', error)
            return
        }

        if ((data as any)?.teaching_days_mode) {
            teachingDaysMode.value = (data as any).teaching_days_mode as 'weekdays' | 'sunday-thursday'
        }
    } catch (err) {
        console.error('Error loading teaching days mode:', err)
        // Use default on any error
        teachingDaysMode.value = 'weekdays'
    }
}

const getEntriesForDay = (day: DayOfWeek): TimetableEntry[] => {
    return timetableEntries.value.filter(entry => entry.day === day)
}

const handleClearAll = async () => {
    if (timetableEntries.value.length === 0) {
        isClearAllModalOpen.value = false
        return
    }
    isClearingAll.value = true
    try {
        const deletePromises = timetableEntries.value.map(entry =>
            deleteTimetableEntry(entry.id)
        )
        await Promise.all(deletePromises)
        isClearAllModalOpen.value = false
    } catch (err) {
        logError('clearing all timetable entries', err)
    } finally {
        isClearingAll.value = false
    }
}


const saveClassSubjects = async (newClassSubjects: UserClassSubjectEntry[]) => {
    const auth = ensureAuthenticated()
    if (!auth) return
    const { user, client } = auth
    try {
        const originalClassSubjects = props.userClassSubjects || []
        const deletedClassSubjects = originalClassSubjects.filter(original => {
            return !newClassSubjects.some(updated =>
                updated.class_id === original.class_id &&
                updated.subject_id === original.subject_id
            )
        })
        const entriesToDelete = timetableEntries.value.filter(entry => {
            return deletedClassSubjects.some(deleted =>
                entry.class_id === deleted.class_id &&
                entry.subject_id === deleted.subject_id
            )
        })
        if (entriesToDelete.length > 0) {
            classSubjectsToDelete.value = deletedClassSubjects
            timetableEntriesToDelete.value = entriesToDelete
            pendingClassSubjectsUpdate.value = newClassSubjects
            isClassSubjectDeleteConfirmModalOpen.value = true
            return
        }
        await proceedWithClassSubjectUpdate(auth, newClassSubjects)
    } catch (err) {
        logError('saving class subjects', err)
    }
}

const proceedWithClassSubjectUpdate = async (passedAuth?: { user: any, client: any }, newClassSubjects?: UserClassSubjectEntry[]) => {
    const auth = passedAuth || ensureAuthenticated()
    if (!auth) return
    const { user, client } = auth
    try {
        if (timetableEntriesToDelete.value.length > 0) {
            const deletePromises = timetableEntriesToDelete.value.map(entry =>
                deleteTimetableEntry(entry.id)
            )
            await Promise.all(deletePromises)
            await fetchTimetableEntries()
        }
        const classSubjectsData = newClassSubjects || pendingClassSubjectsUpdate.value
        const { error } = await client
            .from('profiles')
            .update({
                class_subjects: classSubjectsData
            })
            .eq('id', user.id)
        if (error) throw error
        emit('update-class-subjects')
        closeClassSubjectModal()
        clearClassSubjectDeleteConfirmation()
        showSuccessToast('Kelas & subjek berjaya dikemaskini')
    } catch (err) {
        logError('updating class subjects', err)
        showErrorToast('Gagal mengemaskini kelas & subjek')
    }
}

const clearClassSubjectDeleteConfirmation = () => {
    classSubjectsToDelete.value = []
    timetableEntriesToDelete.value = []
    pendingClassSubjectsUpdate.value = []
    isClassSubjectDeleteConfirmModalOpen.value = false
}

const handleEntrySaved = async (entry: TimetableEntry) => {
    await nextTick()
    await fetchTimetableEntries()
    closeModal()
}

const confirmDeleteEntry = (entry: TimetableEntry) => {
    entryToDelete.value = entry
    isDeleteModalOpen.value = true
}

const handleDeleteEntry = async () => {
    if (!entryToDelete.value) return
    try {
        await deleteTimetableEntry(entryToDelete.value.id)
    } catch (error) {
        logError('deleting timetable entry', error)
    } finally {
        isDeleteModalOpen.value = false
        entryToDelete.value = null
    }
}

watch(() => props.userClassSubjects, async (newClassSubjects) => {
    if (newClassSubjects && newClassSubjects.length > 0) {
        const subjectIds = [...new Set(
            newClassSubjects
                .map(cs => cs.subject_id)
                .filter((id): id is string => !!id && id !== null)
        )];
        if (subjectIds.length > 0) await fetchSubjects(subjectIds)
    }
}, { immediate: true, deep: true })

onMounted(async () => {
    await Promise.all([
        fetchTimeSlots(),
        fetchTimetableEntries(),
        loadTeachingDaysModeFromProfile()
    ])
})
</script>
